import { Controller, Post, Body, Get, Param, Query, Delete, HttpCode, HttpStatus, ForbiddenException, NotFoundException, UseGuards, Logger } from '@nestjs/common';
import { ApiTags, ApiOperation, ApiResponse, ApiBody, ApiParam, ApiBearerAuth } from '@nestjs/swagger';
import { Public } from '../auth/decorators/public.decorator';
import { ActiveUser } from '../auth/decorators/active-user.decorator';
import { Roles } from '../auth/decorators/role.decorator';
import { AuthGuard } from '../auth/guards/auth.guard';
import { RoleGuard } from '../auth/guards/role.guard';
import { EUserRole } from '../user/dto/create-user.dto';
import { User } from '../user/entities/user.entity';
import { WorksheetService } from './worksheet.service';
import { CreateWorksheetDto } from './dto/create-worksheet.dto';
import { ListWorksheetDto } from './dto/list-worksheets.dto';
import { WorksheetCleanupService } from './worksheet-cleanup.service';
import { InjectModel } from '@nestjs/mongoose';
import { Model } from 'mongoose';
import { WorksheetDocument } from '../mongodb/schemas/worksheet-document.schema';
import { WorksheetQuestionService } from './services/worksheet-question.service';
import { AddQuestionToWorksheetDto } from './dto/worksheet-question.dto';

@ApiTags('Worksheet')
@Controller('worksheets')
@UseGuards(AuthGuard, RoleGuard)
@ApiBearerAuth()
export class WorksheetController {
  private readonly logger = new Logger(WorksheetController.name);

  constructor(
    private readonly worksheetService: WorksheetService,
    private readonly worksheetCleanupService: WorksheetCleanupService,
    private readonly worksheetQuestionService: WorksheetQuestionService,
    @InjectModel(WorksheetDocument.name)
    private worksheetDocumentModel: Model<WorksheetDocument>,
  ) {}

  @Post()
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({ summary: 'Create a new worksheet' })
  @ApiBody({ type: CreateWorksheetDto })
  @ApiResponse({ status: 201, description: 'Worksheet created successfully' })
  @ApiResponse({ status: 400, description: 'Bad request' })
  create(@Body() createWorksheetDto: CreateWorksheetDto, @ActiveUser() user: User) {
    return this.worksheetService.create(createWorksheetDto, user);
  }

  @Get('cache/metrics')
  @Public()
  @ApiOperation({ summary: 'Get document cache metrics' })
  @ApiResponse({ status: 200, description: 'Cache metrics retrieved successfully' })
  async getCacheMetrics() {
    try {
      // Get total document count
      const totalDocuments = await this.worksheetDocumentModel.countDocuments();

      // Get count of documents from cache
      const cachedDocuments = await this.worksheetDocumentModel.countDocuments({ fromCache: true });

      // Get count by hit count ranges
      const hitCountRanges = await Promise.all([
        this.worksheetDocumentModel.countDocuments({ hitCount: { $gte: 10 } }),
        this.worksheetDocumentModel.countDocuments({ hitCount: { $gte: 5, $lt: 10 } }),
        this.worksheetDocumentModel.countDocuments({ hitCount: { $gte: 2, $lt: 5 } }),
        this.worksheetDocumentModel.countDocuments({ hitCount: 1 }),
      ]);

      // Get expiring soon count
      const tomorrow = new Date();
      tomorrow.setDate(tomorrow.getDate() + 1);
      const expiringSoon = await this.worksheetDocumentModel.countDocuments({
        expiresAt: { $lt: tomorrow }
      });

      // Get top 5 topics
      const topTopics = await this.worksheetDocumentModel.aggregate([
        { $group: { _id: "$topic", count: { $sum: 1 } } },
        { $sort: { count: -1 } },
        { $limit: 5 }
      ]);

      // Calculate cache hit rate
      const cacheHitRate = totalDocuments > 0 ? (cachedDocuments / totalDocuments) * 100 : 0;

      return {
        totalDocuments,
        cachedDocuments,
        cacheHitRate: `${cacheHitRate.toFixed(2)}%`,
        hitCountDistribution: {
          '10+': hitCountRanges[0],
          '5-9': hitCountRanges[1],
          '2-4': hitCountRanges[2],
          '1': hitCountRanges[3],
        },
        expiringSoon,
        topTopics: topTopics.map(t => ({ topic: t._id, count: t.count })),
        lastCacheWarming: await this.getLastCacheWarmingTime(),
      };
    } catch (error) {
      return {
        error: error.message,
        status: 'Error retrieving cache metrics'
      };
    }
  }

  @Get('cache/warm')
  @Public()
  @ApiOperation({ summary: 'Manually trigger cache warming' })
  @ApiResponse({ status: 200, description: 'Cache warming triggered successfully' })
  async triggerCacheWarming() {
    try {
      // Trigger the cache warming job
      await this.worksheetCleanupService.warmCache();

      return {
        status: 'Cache warming triggered successfully',
        timestamp: new Date().toISOString()
      };
    } catch (error) {
      return {
        error: error.message,
        status: 'Error triggering cache warming'
      };
    }
  }

  @Get()
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({
    summary: 'Get all worksheets with pagination and school-based filtering',
    description: `Retrieves worksheets with pagination support and school-based filtering.

    **School-Based Filtering Behavior:**
    - **Admin users**: Can optionally specify a schoolId to filter worksheets from a specific school. If no schoolId is provided, all worksheets are returned.
    - **Non-admin users** (SCHOOL_MANAGER, TEACHER, INDEPENDENT_TEACHER): Can only access worksheets from their own school. If a schoolId is provided, it must match their own school ID, otherwise a 403 Forbidden error is returned.
    - **Users without school association**: Cannot access any worksheets and will receive empty results.

    **Parameters:**
    - page: Page number for pagination (starts at 1)
    - pageSize: Number of items per page
    - schoolId: Optional UUID to filter worksheets by school (admin users only for cross-school access)`
  })
  @ApiResponse({ 
    status: 200, 
    description: 'Worksheets retrieved successfully',
    schema: {
      properties: {
        items: {
          type: 'array',
          description: 'Array of worksheet items for the current page',
          items: {
            type: 'object',
            // Worksheet properties would be defined here
          }
        },
        meta: {
          type: 'object',
          description: 'Pagination metadata',
          properties: {
            page: {
              type: 'number',
              description: 'Current page number',
              example: 1
            },
            pageSize: {
              type: 'number',
              description: 'Number of items per page',
              example: 10
            },
            total: {
              type: 'number',
              description: 'Total number of items across all pages',
              example: 100
            },
            totalPages: {
              type: 'number',
              description: 'Total number of pages',
              example: 10
            }
          }
        }
      }
    }
  })
  @ApiResponse({
    status: 400,
    description: 'Bad Request - Invalid query parameters (e.g., invalid UUID format for schoolId)'
  })
  @ApiResponse({
    status: 401,
    description: 'Unauthorized - Missing or invalid authentication token'
  })
  @ApiResponse({
    status: 403,
    description: 'Forbidden - User attempted to access worksheets from a different school or user is not associated with any school'
  })
  async findAll(
    @Query() listWorksheetDto: ListWorksheetDto,
    @ActiveUser() user: User,
  ) {
    return this.worksheetService.findAll(listWorksheetDto, user);
  }

  @Get(':id')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({
    summary: 'Get worksheet by ID with school-based access control',
    description: `Retrieves a specific worksheet by ID with school-based access control.

    **Access Control:**
    - **Admin users**: Can access worksheets from any school
    - **Non-admin users**: Can only access worksheets from their own school
    - If a non-admin user attempts to access a worksheet from a different school, a 404 Not Found error is returned (to avoid revealing the existence of the worksheet)`
  })
  @ApiParam({ name: 'id', description: 'Worksheet ID (UUID format)' })
  @ApiResponse({ status: 200, description: 'Worksheet retrieved successfully' })
  @ApiResponse({ status: 401, description: 'Unauthorized - Missing or invalid authentication token' })
  @ApiResponse({ status: 404, description: 'Worksheet not found or user does not have access to this worksheet' })
  findOne(@Param('id') id: string, @ActiveUser() user: User) {
    return this.worksheetService.findOne(id, user);
  }

  @Delete(':id')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({ summary: 'Delete a worksheet' })
  @ApiParam({ name: 'id', description: 'Worksheet ID' })
  @ApiResponse({ status: 204, description: 'Worksheet deleted successfully' })
  @ApiResponse({ status: 403, description: 'Forbidden. User does not have permission to delete this worksheet.' })
  @ApiResponse({ status: 404, description: 'Worksheet not found.' })
  async remove(@Param('id') id: string, @ActiveUser() user: User): Promise<void> {
    // The service handles both finding the worksheet and authorization checks
    // including admin access to all schools
    return this.worksheetService.remove(id, user);
  }

  @Post(':id/questions')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @ApiOperation({
    summary: 'Add a question to a worksheet',
    description: `Add a new question to an existing worksheet with comprehensive validation and access control.

    **Access Control:**
    - **Admin users**: Can add questions to any worksheet
    - **School Manager**: Can add questions to worksheets in their school only
    - **Teacher**: Can add questions to worksheets in their school only
    - **Independent Teacher**: Can add questions to their own worksheets only

    **Validation:**
    - Validates question data structure and required fields
    - Enforces question limit (max 100 questions per worksheet)
    - Ensures school-based data isolation

    **Features:**
    - Real-time updates via WebSocket
    - Audit logging for compliance
    - MongoDB cache synchronization
    - Comprehensive error handling`
  })
  @ApiParam({ name: 'id', description: 'Worksheet ID' })
  @ApiBody({ type: AddQuestionToWorksheetDto })
  @ApiResponse({
    status: 201,
    description: 'Question added successfully',
    schema: {
      type: 'object',
      properties: {
        success: { type: 'boolean', example: true },
        data: {
          type: 'object',
          description: 'The created question object'
        },
        message: { type: 'string', example: 'Question added successfully' }
      }
    }
  })
  @ApiResponse({ status: 400, description: 'Bad request - Invalid question data or limit exceeded' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or school access denied' })
  @ApiResponse({ status: 404, description: 'Worksheet not found' })
  @ApiResponse({ status: 409, description: 'Conflict - Concurrent modification detected' })
  async addQuestion(
    @Param('id') worksheetId: string,
    @Body() questionDto: AddQuestionToWorksheetDto,
    @ActiveUser() user: User
  ) {
    try {
      const question = await this.worksheetQuestionService.addQuestionToWorksheet(
        worksheetId,
        questionDto,
        {
          sub: user.id,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId || undefined
        }
      );

      return {
        success: true,
        data: question,
        message: 'Question added successfully'
      };
    } catch (error) {
      // Log the error for debugging
      console.error(`Error adding question to worksheet ${worksheetId}:`, error);
      throw error; // Re-throw to let NestJS handle the HTTP response
    }
  }

  @Delete(':id/questions/:questionId')
  @Roles(EUserRole.ADMIN, EUserRole.SCHOOL_MANAGER, EUserRole.TEACHER, EUserRole.INDEPENDENT_TEACHER)
  @HttpCode(HttpStatus.NO_CONTENT)
  @ApiOperation({
    summary: 'Remove a question from a worksheet',
    description: `Remove an existing question from a worksheet with comprehensive validation and access control.

    **Access Control:**
    - **Admin users**: Can remove questions from any worksheet
    - **School Manager**: Can remove questions from worksheets in their school only
    - **Teacher**: Can remove questions from worksheets in their school only
    - **Independent Teacher**: Can remove questions from their own worksheets only

    **Validation:**
    - Validates that the question exists in the specified worksheet
    - Prevents removal if it would result in zero questions (minimum 1 required)
    - Ensures school-based data isolation
    - Validates user permissions before allowing removal

    **Side Effects:**
    - Reorders remaining questions to maintain sequential order
    - Updates MongoDB cache for the worksheet
    - Emits real-time WebSocket event to notify collaborators
    - Creates audit log entry for the removal
    - Updates worksheet metadata (totalQuestions, lastModifiedBy)

    **Real-time Updates:**
    - Broadcasts 'question_removed' event to all users viewing the worksheet
    - Includes updated question count and worksheet metadata`
  })
  @ApiParam({ name: 'id', description: 'Worksheet ID' })
  @ApiParam({ name: 'questionId', description: 'Question ID to remove' })
  @ApiResponse({ status: 204, description: 'Question removed successfully' })
  @ApiResponse({ status: 400, description: 'Bad request - Cannot remove last question or invalid parameters' })
  @ApiResponse({ status: 403, description: 'Forbidden - Insufficient permissions or school access denied' })
  @ApiResponse({ status: 404, description: 'Worksheet or question not found' })
  @ApiResponse({ status: 409, description: 'Conflict - Concurrent modification detected' })
  async removeQuestion(
    @Param('id') worksheetId: string,
    @Param('questionId') questionId: string,
    @ActiveUser() user: User
  ): Promise<void> {
    try {
      await this.worksheetQuestionService.removeQuestionFromWorksheet(
        worksheetId,
        questionId,
        {
          sub: user.id,
          email: user.email,
          role: user.role,
          schoolId: user.schoolId || undefined
        }
      );
    } catch (error) {
      this.logger.error(`Failed to remove question ${questionId} from worksheet ${worksheetId}`, error);

      // Re-throw the error to let NestJS handle the HTTP response
      throw error;
    }
  }

  /**
   * Helper method to get the last cache warming time
   */
  private async getLastCacheWarmingTime(): Promise<string> {
    try {
      // Find the most recent cache-warming document
      const latestWarming = await this.worksheetDocumentModel
        .findOne({ worksheetId: { $regex: /^cache-warming-/ } })
        .sort({ createdAt: -1 });

      return latestWarming ? latestWarming.createdAt.toISOString() : 'Never';
    } catch (error) {
      return 'Unknown';
    }
  }
}
