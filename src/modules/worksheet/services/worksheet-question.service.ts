import { 
  Injectable, 
  NotFoundException, 
  BadRequestException, 
  ForbiddenException,
  ConflictException,
  Logger 
} from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { InjectModel } from '@nestjs/mongoose';
import { Repository } from 'typeorm';
import { Model } from 'mongoose';
import { v4 as uuidv4 } from 'uuid';

import { Worksheet } from '../entities/worksheet.entity';
import { WorksheetQuestionDocument } from '../../mongodb/schemas/worksheet-question-document.schema';
import { CreateExerciseQuestionDto } from '../dto/exercise-question.dto';
import { AddQuestionToWorksheetDto } from '../dto/worksheet-question.dto';
import { IExerciseQuestion } from '../../../shared/interfaces/exercise-question.interface';
import { EUserRole } from '../../user/dto/create-user.dto';
import { WorksheetQuestionAuditService } from './worksheet-question-audit.service';
import { SocketGateway } from '../../socket/socket.gateway';

export interface UserContext {
  sub: string;
  email: string;
  role: EUserRole;
  schoolId?: string;
}

@Injectable()
export class WorksheetQuestionService {
  private readonly logger = new Logger(WorksheetQuestionService.name);

  constructor(
    @InjectRepository(Worksheet)
    private readonly worksheetRepository: Repository<Worksheet>,
    @InjectModel(WorksheetQuestionDocument.name)
    private readonly worksheetQuestionModel: Model<WorksheetQuestionDocument>,
    private readonly auditService: WorksheetQuestionAuditService,
    private readonly socketGateway: SocketGateway,
  ) {}

  /**
   * Add a new question to a worksheet
   */
  async addQuestionToWorksheet(
    worksheetId: string,
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext
  ): Promise<IExerciseQuestion> {
    this.logger.log(`Adding question to worksheet ${worksheetId} by user ${user.sub}`);

    // Step 1: Validate worksheet access and get worksheet
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Step 2: Check question limit
    await this.validateQuestionLimit(worksheet);

    // Step 3: Create the question
    const newQuestion = await this.createQuestion(questionDto, user, worksheet);

    // Step 4: Update worksheet in database
    await this.updateWorksheetWithNewQuestion(worksheet, newQuestion, user);

    // Step 5: Update MongoDB cache
    await this.updateQuestionCache(worksheetId, worksheet.questions, user);

    // Step 6: Emit real-time update
    await this.emitQuestionUpdate(worksheetId, 'question_added', {
      question: newQuestion,
      totalQuestions: worksheet.questions.length,
      worksheetId
    }, user.sub);

    // Step 7: Log audit event
    await this.auditService.logQuestionAdded(worksheetId, newQuestion.id!, user);

    this.logger.log(`Successfully added question ${newQuestion.id} to worksheet ${worksheetId}`);
    return newQuestion;
  }

  /**
   * Validate user access to worksheet and return worksheet
   */
  private async validateWorksheetAccess(
    worksheetId: string, 
    user: UserContext
  ): Promise<Worksheet> {
    const worksheet = await this.worksheetRepository.findOne({
      where: { id: worksheetId },
      relations: ['selectedOptions']
    });

    if (!worksheet) {
      throw new NotFoundException(`Worksheet with ID ${worksheetId} not found`);
    }

    // Admin has access to all worksheets
    if (user.role === EUserRole.ADMIN) {
      return worksheet;
    }

    // School-based access control
    if (user.role === EUserRole.SCHOOL_MANAGER) {
      if (!user.schoolId) {
        throw new ForbiddenException('School manager must be assigned to a school');
      }
      if (worksheet.schoolId !== user.schoolId) {
        throw new ForbiddenException('Cannot modify questions from different school');
      }
      return worksheet;
    }

    // Independent teacher can only modify their own worksheets
    if (user.role === EUserRole.INDEPENDENT_TEACHER) {
      if (worksheet.createdBy !== user.sub) {
        throw new ForbiddenException('Can only modify your own worksheets');
      }
      return worksheet;
    }

    // Regular teacher can modify worksheets in their school
    if (user.role === EUserRole.TEACHER) {
      if (!user.schoolId || worksheet.schoolId !== user.schoolId) {
        throw new ForbiddenException('Access denied to worksheet from different school');
      }
      return worksheet;
    }

    throw new ForbiddenException('Insufficient permissions to modify worksheet questions');
  }

  /**
   * Validate that adding a question won't exceed the limit
   */
  private async validateQuestionLimit(worksheet: Worksheet): Promise<void> {
    const currentQuestionCount = worksheet.questions?.length || 0;
    const maxQuestions = worksheet.maxQuestions || 100;

    if (currentQuestionCount >= maxQuestions) {
      throw new BadRequestException(
        `Question limit exceeded. Current: ${currentQuestionCount}, Maximum: ${maxQuestions}`
      );
    }
  }

  /**
   * Create a new question from DTO
   */
  private async createQuestion(
    questionDto: AddQuestionToWorksheetDto,
    user: UserContext,
    worksheet: Worksheet
  ): Promise<IExerciseQuestion> {
    const questionId = uuidv4();
    const currentQuestions = worksheet.questions || [];
    const position = questionDto.position || currentQuestions.length + 1;

    const newQuestion: IExerciseQuestion = {
      id: questionId,
      type: questionDto.type,
      content: questionDto.content,
      options: questionDto.options,
      answer: questionDto.answer,
      explain: questionDto.explain,
      subject: questionDto.subject,
      parentSubject: questionDto.parentSubject,
      childSubject: questionDto.childSubject,
      topic: questionDto.topic,
      subtopic: questionDto.subtopic,
      grade: questionDto.grade,
      difficulty: questionDto.difficulty,
      media: questionDto.media,
      imagePrompt: questionDto.imagePrompt,
      imageUrl: questionDto.imageUrl,
      status: questionDto.status as any, // Convert worksheet status to question status
      isPublic: questionDto.isPublic,
      metadata: questionDto.metadata,
      // Audit fields
      audit: {
        createdBy: user.sub,
        createdAt: new Date(),
        updatedBy: user.sub,
        updatedAt: new Date(),
        version: 1
      },
      // School association
      schoolId: user.schoolId
    };

    return newQuestion;
  }

  /**
   * Update worksheet with new question
   */
  private async updateWorksheetWithNewQuestion(
    worksheet: Worksheet,
    newQuestion: IExerciseQuestion,
    user: UserContext
  ): Promise<void> {
    if (!worksheet.questions) {
      worksheet.questions = [];
    }

    worksheet.questions.push(newQuestion);
    worksheet.totalQuestions = worksheet.questions.length;
    worksheet.lastModifiedBy = user.sub;

    await this.worksheetRepository.save(worksheet);
  }

  /**
   * Update MongoDB question cache
   */
  private async updateQuestionCache(
    worksheetId: string,
    questions: IExerciseQuestion[],
    user: UserContext
  ): Promise<void> {
    try {
      await this.worksheetQuestionModel.findOneAndUpdate(
        { worksheetId },
        {
          $set: {
            questions,
            totalQuestions: questions.length,
            lastModifiedBy: user.sub,
            lastModifiedAt: new Date(),
            schoolId: user.schoolId
          },
          $inc: { version: 1 }
        },
        { upsert: true, new: true }
      );
    } catch (error) {
      this.logger.error(`Failed to update question cache for worksheet ${worksheetId}`, error);
      // Don't throw error - cache update failure shouldn't fail the operation
    }
  }

  /**
   * Remove a question from a worksheet
   */
  async removeQuestionFromWorksheet(
    worksheetId: string,
    questionId: string,
    user: UserContext
  ): Promise<void> {
    this.logger.log(`Removing question ${questionId} from worksheet ${worksheetId} by user ${user.sub}`);

    // Step 1: Validate worksheet access and get worksheet
    const worksheet = await this.validateWorksheetAccess(worksheetId, user);

    // Step 2: Find and validate the question exists in the worksheet
    const questionToRemove = await this.findQuestionInWorksheet(worksheet, questionId);

    // Step 3: Validate minimum questions requirement
    await this.validateMinimumQuestions(worksheet);

    // Step 4: Remove question from worksheet and reorder remaining questions
    await this.removeQuestionAndReorder(worksheet, questionId, user);

    // Step 5: Update MongoDB cache
    await this.updateQuestionCache(worksheetId, worksheet.questions, user);

    // Step 6: Emit real-time update
    await this.emitQuestionUpdate(worksheetId, 'question_removed', {
      questionId,
      totalQuestions: worksheet.questions.length,
      worksheetId
    }, user.sub);

    // Step 7: Log audit event
    await this.auditService.logQuestionRemoved(worksheetId, questionId, questionToRemove, user);

    this.logger.log(`Successfully removed question ${questionId} from worksheet ${worksheetId}`);
  }

  /**
   * Find a question in the worksheet and return it
   */
  private async findQuestionInWorksheet(
    worksheet: Worksheet,
    questionId: string
  ): Promise<IExerciseQuestion> {
    const questions = worksheet.questions || [];
    const question = questions.find(q => q.id === questionId);

    if (!question) {
      throw new NotFoundException(
        `Question with ID ${questionId} not found in worksheet ${worksheet.id}`
      );
    }

    return question;
  }

  /**
   * Validate that removing a question won't violate minimum requirements
   */
  private async validateMinimumQuestions(worksheet: Worksheet): Promise<void> {
    const currentQuestionCount = worksheet.questions?.length || 0;
    const minQuestions = 1; // Minimum 1 question per worksheet

    if (currentQuestionCount <= minQuestions) {
      throw new BadRequestException(
        `Cannot remove question. Worksheet must have at least ${minQuestions} question(s). Current count: ${currentQuestionCount}`
      );
    }
  }

  /**
   * Remove question from worksheet and reorder remaining questions
   */
  private async removeQuestionAndReorder(
    worksheet: Worksheet,
    questionId: string,
    user: UserContext
  ): Promise<void> {
    if (!worksheet.questions) {
      throw new NotFoundException('No questions found in worksheet');
    }

    // Remove the question
    const originalLength = worksheet.questions.length;
    worksheet.questions = worksheet.questions.filter(q => q.id !== questionId);

    if (worksheet.questions.length === originalLength) {
      throw new NotFoundException(`Question with ID ${questionId} not found in worksheet`);
    }

    // Reorder remaining questions to maintain sequential order
    worksheet.questions.forEach((question, index) => {
      question.order = index + 1;
      // Update audit info
      if (question.audit) {
        question.audit.updatedBy = user.sub;
        question.audit.updatedAt = new Date();
        question.audit.version = (question.audit.version || 1) + 1;
      }
    });

    // Update worksheet metadata
    worksheet.totalQuestions = worksheet.questions.length;
    worksheet.lastModifiedBy = user.sub;

    // Save the updated worksheet
    await this.worksheetRepository.save(worksheet);
  }

  /**
   * Emit real-time update via WebSocket
   */
  private async emitQuestionUpdate(
    worksheetId: string,
    event: string,
    data: any,
    excludeUserId?: string
  ): Promise<void> {
    try {
      // Emit to all users subscribed to this worksheet except the one who made the change
      this.socketGateway.server.to(`worksheet-${worksheetId}`).emit(event, {
        ...data,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      this.logger.error(`Failed to emit ${event} for worksheet ${worksheetId}`, error);
      // Don't throw error - WebSocket failure shouldn't fail the operation
    }
  }
}
